"""Base configuration settings for TargetWise."""
import os
from pathlib import Path
from typing import Any, Dict, List, Optional
from pydantic import AnyHttpUrl, EmailStr, validator, PostgresDsn
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Core Application Settings
    PROJECT_NAME: str = "TargetWise"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")

    # Facebook API Settings
    FACEBOOK_API_VERSION: str = "v17.0"
    FACEBOOK_API_BASE_URL: str = f"https://graph.facebook.com/v17.0"
    FACEBOOK_AD_ACCOUNT_ID: str = os.getenv("FACEBOOK_AD_ACCOUNT_ID", "")
    FACEBOOK_ACCESS_TOKEN: str = os.getenv("FACEBOOK_ACCESS_TOKEN", "")

    # Rate limiting settings
    API_RATE_LIMIT_SCORE: int = 60
    API_RATE_LIMIT_DECAY_SECONDS: int = 300

    # Targeting settings
    DEFAULT_COUNTRY_CODE: str = "US"
    DEFAULT_AGE_MIN: int = 18
    DEFAULT_AGE_MAX: int = 65
    TARGET_AUDIENCE_SIZE_MIN: int = 4000000
    TARGET_AUDIENCE_SIZE_MAX: int = 5000000

    # Column definitions for Algorithmic Targeting 2.0
    TARGETING_COLUMNS: List[Dict[str, Any]] = [
        {"id": 1, "name": "Keyword Mix < 1M", "default_style": 1},
        {"id": 2, "name": "Keyword Mix > 1M", "default_style": 2},
        {"id": 3, "name": "Magazine Flex", "default_style": 1},
        {"id": 4, "name": "Websites & Keywords", "default_style": 2},
        {"id": 5, "name": "Niche Categories", "default_style": 2},
        {"id": 6, "name": "Passionate Flex", "default_style": 1},
        {"id": 7, "name": "TV Shows & Groups", "default_style": 3},
        {"id": 8, "name": "Core 'Neck' Flex", "default_style": 3},
        {"id": 9, "name": "Related Niche", "default_style": 3},
        {"id": 10, "name": "Buyers Flex", "default_style": 1},
        {"id": 11, "name": "Affinity Flex", "default_style": 2},
        {"id": 12, "name": "Store 'Light' Flex", "default_style": 3},
    ]

    # Targeting styles
    TARGETING_STYLES: Dict[int, str] = {
        1: "OR-Only",
        2: "Narrowed",
        3: "Single-Interest",
    }

    # NLP model settings
    NLP_MODEL_NAME: str = "all-MiniLM-L6-v2"

    # File storage settings
    TEMP_DIR: str = os.path.join(Path(__file__).parent.parent, "temp")
    OUTPUT_DIR: str = os.path.join(Path(__file__).parent.parent, "output")

    # Security
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    SERVER_NAME: Optional[str] = None
    SERVER_HOST: AnyHttpUrl = "http://localhost:8000"

    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",  # Frontend URL
        "http://localhost:8000",  # Backend URL
    ]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str] | str:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "targetwise")
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict[str, any]) -> any:
        if isinstance(v, str):
            return v

        user = values.get("POSTGRES_USER", "")
        password = values.get("POSTGRES_PASSWORD", "")
        host = values.get("POSTGRES_SERVER", "")
        db = values.get("POSTGRES_DB", "")

        return f"postgresql://{user}:{password}@{host}/{db}"

    # Facebook API
    FACEBOOK_API_VERSION: str = os.getenv("FACEBOOK_API_VERSION", "v17.0")
    FACEBOOK_APP_ID: str = os.getenv("FACEBOOK_APP_ID", "")
    FACEBOOK_APP_SECRET: str = os.getenv("FACEBOOK_APP_SECRET", "")
    FACEBOOK_ACCESS_TOKEN: str = os.getenv("FACEBOOK_ACCESS_TOKEN", "")
    FACEBOOK_AD_ACCOUNT_ID: str = os.getenv("FACEBOOK_AD_ACCOUNT_ID", "")

    # Redis
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # Email
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None

    @validator("EMAILS_FROM_NAME")
    def get_project_name(cls, v: Optional[str], values: dict[str, any]) -> str:
        if not v:
            return values["PROJECT_NAME"]
        return v

    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48
    EMAIL_TEMPLATES_DIR: str = "/app/app/email-backend/templates/build"
    EMAILS_ENABLED: bool = False

    @validator("EMAILS_ENABLED", pre=True)
    def get_emails_enabled(cls, v: bool, values: dict[str, any]) -> bool:
        return bool(
            values.get("SMTP_HOST")
            and values.get("SMTP_PORT")
            and values.get("EMAILS_FROM_EMAIL")
        )

    # First Superuser
    FIRST_SUPERUSER: EmailStr = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = ""
    USERS_OPEN_REGISTRATION: bool = False

    class Config:
        case_sensitive = True
        env_file = ".env"
        extra = "allow"  # Allow extra fields from environment

settings = Settings()
