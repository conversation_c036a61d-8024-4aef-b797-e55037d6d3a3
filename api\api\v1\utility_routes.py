# // DOC: docs/micro-tools/search-bulk-feature.md
import logging
import os
import tempfile
import uuid
from datetime import datetime
from typing import List, Optional

import pandas as pd
from fastapi import APIRouter, BackgroundTasks, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import FileResponse

from api.schemas import (
    ExportCSVRequest,
    InterestItem,
    ReachEstimateRequest,
    ReachEstimateResponse,
    SampleCSVType,
    TargetingRequest,
    TargetingResponse,
    TargetingSheet,
    TargetingStyle,
    TargetingValidationResponse,
)
from api.services import (
    InterestSearchService,
    SuggestionService,
    ReachEstimationService,
)
from api.services.interest_classifier import InterestClassifier
from api.services.targeting_builder import TargetingBuilder
from api.utils.monitoring import api_monitor

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/health")
async def health_check():
    """Health check endpoint for monitoring API status."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "TargetWise API",
        "version": "2.0.0"
    }

@router.post("/targeting/build", response_model=TargetingResponse)
async def build_targeting_sheet(
    background_tasks: BackgroundTasks,
    file: Optional[UploadFile] = File(None),
    seed_interests: Optional[str] = Form(None),
    country_code: str = Form("US"),
    age_min: int = Form(18),
    age_max: int = Form(65),
):
    """Build a 12-column Algorithmic Targeting 2.0 sheet."""
    if not file and not seed_interests:
        raise HTTPException(status_code=400, detail="Either a file or seed interests must be provided")

    job_id = str(uuid.uuid4())
    job_dir = os.path.join(tempfile.gettempdir(), job_id)
    os.makedirs(job_dir, exist_ok=True)

    interests: List[str] = []

    if file:
        try:
            file_path = os.path.join(job_dir, file.filename or "uploaded_file.csv")
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "wb") as f:
                f.write(await file.read())
        except Exception as e:
            logger.error(f"Error saving uploaded file: {str(e)}")
            if not seed_interests:
                raise HTTPException(status_code=400, detail=f"Error processing uploaded file: {str(e)}")
        try:
            df = pd.read_csv(file_path)
            if "interest" in df.columns:
                interests.extend(df["interest"].tolist())
            elif "interests" in df.columns:
                interests.extend(df["interests"].tolist())
            elif "keyword" in df.columns:
                interests.extend(df["keyword"].tolist())
            else:
                interests.extend(df.iloc[:, 0].tolist())
        except Exception as e:
            logger.error(f"Error parsing CSV file: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Error parsing CSV file: {str(e)}")

    if seed_interests:
        interests.extend([i.strip() for i in seed_interests.split(",")])

    interests = [i for i in interests if i and isinstance(i, str)]
    interests = list(set(interests))

    if not interests:
        raise HTTPException(status_code=400, detail="No valid interests found in the input data")

    job_info = {
        "id": job_id,
        "status": "processing",
        "created_at": datetime.now().isoformat(),
        "interests": interests,
        "country_code": country_code,
        "age_min": age_min,
        "age_max": age_max,
        "output_file": None,
    }

    with open(os.path.join(job_dir, "job_info.json"), "w") as f:
        import json
        json.dump(job_info, f)

    background_tasks.add_task(
        process_targeting_sheet,
        job_id=job_id,
        interests=interests,
        country_code=country_code,
        age_min=age_min,
        age_max=age_max,
    )

    return TargetingResponse(
        job_id=job_id,
        status="processing",
        message="Your targeting sheet is being processed. Check the status using the job ID.",
    )

@router.get("/targeting/status/{job_id}", response_model=TargetingResponse)
async def get_targeting_status(job_id: str):
    job_dir = os.path.join(tempfile.gettempdir(), job_id)
    if not os.path.exists(job_dir):
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

    with open(os.path.join(job_dir, "job_info.json"), "r") as f:
        import json
        job_info = json.load(f)

    return TargetingResponse(
        job_id=job_id,
        status=job_info["status"],
        message=f"Job status: {job_info['status']}",
        output_file=job_info.get("output_file"),
    )


@router.get("/targeting/download/{job_id}")
async def download_targeting_sheet(job_id: str):
    job_dir = os.path.join(tempfile.gettempdir(), job_id)
    if not os.path.exists(job_dir):
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

    with open(os.path.join(job_dir, "job_info.json"), "r") as f:
        import json
        job_info = json.load(f)

    if job_info["status"] != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Job is not completed yet. Current status: {job_info['status']}",
        )

    output_file = job_info.get("output_file")
    if not output_file or not os.path.exists(output_file):
        raise HTTPException(status_code=404, detail="Output file not found")

    return FileResponse(
        output_file,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        filename=f"targeting_sheet_{job_id}.xlsx",
    )

@router.get("/targeting/sample-csv")
async def download_sample_csv(
    type: SampleCSVType = Query(SampleCSVType.SEARCH, description="Type of sample CSV to download"),
):
    # Create temporary samples directory
    samples_dir = os.path.join(tempfile.gettempdir(), "targetwise_samples")
    if not os.path.exists(samples_dir):
        os.makedirs(samples_dir, exist_ok=True)

    if type == SampleCSVType.SEARCH:
        sample_file_path = os.path.join(samples_dir, "search_keywords.csv")
        filename = "sample_search_keywords.csv"
    elif type == SampleCSVType.SUGGESTIONS:
        sample_file_path = os.path.join(samples_dir, "suggestion_keywords.csv")
        filename = "sample_suggestion_keywords.csv"
    else:
        sample_file_path = os.path.join(samples_dir, "interest_list.csv")
        filename = "sample_interests.csv"

    # Check if the file exists
    if not os.path.exists(sample_file_path):
        # Create the file with sample data
        if type == SampleCSVType.SEARCH:
            df = pd.DataFrame(
                {
                    "keyword": [
                        "home theater",
                        "projector",
                        "4k resolution",
                        "smart tv",
                        "streaming device",
                        "surround sound",
                        "bluetooth speaker",
                        "wireless headphones",
                        "gaming console",
                        "virtual reality",
                    ]
                }
            )
        elif type == SampleCSVType.SUGGESTIONS:
            df = pd.DataFrame(
                {
                    "keyword": [
                        "home theater",
                        "projector",
                        "4k resolution",
                        "surround sound",
                        "streaming device",
                        "bluetooth speaker",
                        "wireless headphones",
                        "gaming console",
                        "virtual reality",
                        "smart home",
                    ]
                }
            )
        else:
            df = pd.DataFrame(
                {
                    "interest": [
                        "Home theater",
                        "Projector",
                        "4K resolution",
                        "Smart TV",
                        "Streaming services",
                        "Surround sound",
                        "Bluetooth speaker",
                        "Wireless headphones",
                        "Gaming console",
                        "Virtual reality",
                    ]
                }
            )

        # Save the file
        df.to_csv(sample_file_path, index=False)

    return FileResponse(sample_file_path, media_type="text/csv", filename=filename)

@router.post("/targeting/reachestimate", response_model=ReachEstimateResponse)
async def reach_estimate(req: ReachEstimateRequest):
    from api.models.admin import AdminStore
    admin_store = AdminStore()
    credentials = admin_store.get_credentials()

    fb = ReachEstimationService(
        api_version=credentials.api_version,
        ad_account_id=credentials.ad_account_id,
        access_token=credentials.access_token,
    )

    interests = [InterestItem(id=i, name="") for i in req.seed_interests]
    narrow = InterestItem(id=req.narrow_interest, name="") if req.narrow_interest else None
    return await fb.estimate_reach(interests, narrow, req.country_code, req.age_min, req.age_max)


@router.post("/targeting/validate", response_model=TargetingValidationResponse)
async def validate_targeting(req: TargetingRequest):
    from api.models.admin import AdminStore
    admin_store = AdminStore()
    credentials = admin_store.get_credentials()

    fb = ReachEstimationService(
        api_version=credentials.api_version,
        ad_account_id=credentials.ad_account_id,
        access_token=credentials.access_token,
    )

    interests = [InterestItem(id=i, name="") for i in req.seed_interests]
    return await fb.validate_targeting(interests, req.country_code, req.age_min, req.age_max)


@router.post("/export-csv")
async def export_to_csv(req: ExportCSVRequest):
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{req.format}") as temp_file:
            temp_path = temp_file.name

        df = pd.DataFrame(req.data)

        if req.format == "csv":
            df.to_csv(temp_path, index=False)
            media_type = "text/csv"
            filename = f"{req.type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        else:
            df.to_excel(temp_path, index=False)
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filename = f"{req.type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return FileResponse(temp_path, media_type=media_type, filename=filename)
    except Exception as e:
        logger.error(f"Error exporting to CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error exporting to CSV: {str(e)}")


@router.get("/quota")
async def get_api_quota():
    try:
        from api.models.admin import AdminStore
        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        fb_service = ReachEstimationService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )

        if hasattr(fb_service, "api_handler") and hasattr(fb_service.api_handler, "rate_limit_score"):
            score = fb_service.api_handler.rate_limit_score
            max_score = fb_service.api_handler.api_rate_limit_score
            reset_in = 300
        else:
            score = 0
            max_score = 60
            reset_in = 300

        return {"success": True, "score": score, "max": max_score, "reset_in": reset_in}
    except Exception as e:
        logger.error(f"Error getting API quota: {str(e)}")
        return {"success": False, "score": 0, "max": 60, "reset_in": 300, "error": str(e)}


@router.get("/admin/api-stats")
async def get_api_stats():
    try:
        stats = api_monitor.get_stats()
        return {"success": True, "data": stats}
    except Exception as e:
        logger.error(f"Error getting API stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting API stats: {str(e)}")


@router.post("/admin/reset-api-stats")
async def reset_api_stats():
    try:
        api_monitor.reset_stats()
        return {"success": True, "message": "API statistics reset successfully"}
    except Exception as e:
        logger.error(f"Error resetting API stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error resetting API stats: {str(e)}")

async def process_targeting_sheet(job_id: str, interests: List[str], country_code: str, age_min: int, age_max: int):
    job_dir = os.path.join(tempfile.gettempdir(), job_id)
    try:
        update_job_status(job_id, "fetching_interests")
        from api.models.admin import AdminStore
        admin_store = AdminStore()
        credentials = admin_store.get_credentials()

        search_service = InterestSearchService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )
        suggestion_service = SuggestionService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )
        reach_service = ReachEstimationService(
            api_version=credentials.api_version,
            ad_account_id=credentials.ad_account_id,
            access_token=credentials.access_token,
        )
        classifier = InterestClassifier()
        builder = TargetingBuilder()

        interest_data = await search_service.fetch_interests(interests, country_code)
        suggestions = await suggestion_service.fetch_suggestions(interest_data, country_code)

        targeting_sheet = TargetingSheet(
            columns=[],
            country_code=country_code,
            age_min=age_min,
            age_max=age_max,
            seed_interests=interests,
            total_interests_found=len(interest_data),
            total_suggestions_found=len(suggestions),
        )

        unique_suggestions = []
        suggestion_ids = set()
        for suggestion in suggestions:
            if suggestion.id not in suggestion_ids:
                suggestion_ids.add(suggestion.id)
                unique_suggestions.append(suggestion)

        all_interests = interest_data + unique_suggestions
        classified_interests = classifier.classify_interests(all_interests)

        update_job_status(job_id, "building_targeting")
        targeting_sheet = builder.build_targeting_sheet(classified_interests, country_code, age_min, age_max)

        update_job_status(job_id, "estimating_reach")
        try:
            targeting_sheet = await reach_service.estimate_and_adjust_reach(targeting_sheet, country_code, age_min, age_max)
        except Exception as e:
            logger.error(f"Error in reach estimation: {str(e)}")
            update_job_status(job_id, "using_draft_mode")
            for column in targeting_sheet.columns:
                if column.targeting_style == TargetingStyle.SINGLE_INTEREST and column.interests:
                    column.estimated_reach_lower = column.interests[0].audience_size_lower_bound or 0
                    column.estimated_reach_upper = column.interests[0].audience_size_upper_bound or 0
                elif column.targeting_style == TargetingStyle.OR_ONLY:
                    total_lower = sum(i.audience_size_lower_bound or 0 for i in column.interests)
                    total_upper = sum(i.audience_size_upper_bound or 0 for i in column.interests)
                    column.estimated_reach_lower = int(total_lower * 0.7)
                    column.estimated_reach_upper = int(total_upper * 0.7)
                elif column.targeting_style == TargetingStyle.NARROWED:
                    if column.narrow_interest and column.interests:
                        narrow_size = column.narrow_interest.audience_size_upper_bound or 0
                        column.estimated_reach_lower = int(narrow_size * 0.2)
                        column.estimated_reach_upper = int(narrow_size * 0.4)

            for column in targeting_sheet.columns:
                column.needs_review = True
                column.review_reason = "DRAFT MODE: Sizes are estimates only"

        update_job_status(job_id, "validating_quality_gates")
        validation_result = builder.validate_quality_gates(targeting_sheet)
        if validation_result["blocking_issues"]:
            update_job_status(job_id, "failed", error="Quality gate validation failed: " + ", ".join(validation_result["messages"]))
            return

        update_job_status(job_id, "generating_files")

        excel_file_path = os.path.join(job_dir, f"targeting_sheet_{job_id}.xlsx")
        builder.generate_excel(targeting_sheet, excel_file_path)
        logger.info(f"Excel file generated at {excel_file_path}")

        update_job_status(job_id, "completed", output_file=excel_file_path)
        logger.info(f"Job {job_id} completed successfully")
    except Exception as e:
        logger.error(f"Error processing job {job_id}: {str(e)}")
        update_job_status(job_id, "failed", error=str(e))


def update_job_status(job_id: str, status: str, output_file: str | None = None, error: str | None = None):
    job_dir = os.path.join(tempfile.gettempdir(), job_id)
    with open(os.path.join(job_dir, "job_info.json"), "r") as f:
        import json
        job_info = json.load(f)
    job_info["status"] = status
    job_info["updated_at"] = datetime.now().isoformat()
    if output_file:
        job_info["output_file"] = output_file
    if error:
        job_info["error"] = error
    with open(os.path.join(job_dir, "job_info.json"), "w") as f:
        import json
        json.dump(job_info, f)

